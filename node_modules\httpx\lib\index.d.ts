import { IncomingMessage, Agent, OutgoingHttpHeaders } from "http";
import { Readable } from "stream";

export interface Options {
    'method'?: string;
    'readTimeout'?: number;
    'connectTimeout'?: number;
    'timeout'?: number;
    'agent'?: Agent;
    'headers'?: OutgoingHttpHeaders;
    'rejectUnauthorized'?: boolean;
    'compression'?: boolean;
    'beforeRequest'?(options: Options): void;
    'data'?: string | Buffer | Readable | undefined;
    'key'?: string;
    'cert'?: string;
    'ca'?: string;
}

export function request(url: string, options: Options): Promise<IncomingMessage>;

export function read(response: IncomingMessage, encoding: string): Promise<string|Buffer>;

export interface Event {
    'data'?: string;
    'id'?: string;
    'event'?: string;
    'retry'?: number;
}

export function readAsSSE(response: IncomingMessage): AsyncGenerator<Event, void, unknown>;
