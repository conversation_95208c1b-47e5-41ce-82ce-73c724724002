{"name": "kitx", "version": "1.3.0", "description": "toolkit", "main": "lib/index.js", "directories": {"test": "test"}, "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "git+https://github.com/JacksonTian/kitx.git"}, "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/JacksonTian/kitx/issues"}, "homepage": "https://github.com/JacksonTian/kitx#readme", "keywords": ["toolkit", "kit"], "devDependencies": {"co-mocha": "~1.1.2", "coveralls": "~2.11.9", "expect.js": "~0.3.1", "istanbul": "~0.4.2", "mocha": "^3.4.2"}, "files": ["lib"]}