{"name": "@alicloud/pop-core", "version": "1.8.0", "description": "AliCloud POP SDK core", "main": "index.js", "types": "lib/rpc.d.ts", "scripts": {"lint": "eslint --fix lib test", "test": "mocha -R spec test/*.test.js", "test-cov": "nyc -r=html -r=text -r=lcov mocha -t 3000 -R spec test/*.test.js", "test-integration": "mocha -R spec test/*.integration.js", "ci": "npm run lint && npm run test-cov"}, "keywords": ["<PERSON><PERSON>", "AliCloud", "OpenAPI", "POP", "SDK", "Core"], "author": "<PERSON>", "license": "MIT", "dependencies": {"debug": "^3.1.0", "httpx": "^2.1.2", "json-bigint": "^1.0.0", "kitx": "^1.2.1", "xml2js": "^0.5.0"}, "files": ["lib", "index.js"], "devDependencies": {"eslint": "^6.6.0", "expect.js": "^0.3.1", "mocha": "^4", "muk": "^0.5.3", "nyc": "^12.0.2", "rewire": "^4.0.1"}, "directories": {"test": "test"}, "engines": {"node": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/aliyun/openapi-core-nodejs-sdk.git"}, "bugs": {"url": "https://github.com/aliyun/openapi-core-nodejs-sdk/issues"}, "homepage": "https://github.com/aliyun/openapi-core-nodejs-sdk#readme"}