{"name": "httpx", "version": "2.3.3", "description": "http(s) module with power", "main": "lib/index.js", "types": "lib/index.d.ts", "scripts": {"lint": "eslint --fix lib test", "test": "mocha --reporter spec --timeout 3000 test/*.test.js", "test-cov": "nyc -r=lcov -r=html -r=text -r=json mocha -t 3000 -R spec test/*.test.js"}, "repository": {"type": "git", "url": "https://github.com/JacksonTian/httpx.git"}, "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/JacksonTian/httpx/issues"}, "homepage": "https://github.com/JacksonTian/httpx", "devDependencies": {"eslint": "^8.26.0", "mocha": "^10", "nyc": "^15.1.0", "socks-proxy-agent": "^7", "socksv5": "^0.0.6"}, "dependencies": {"@types/node": "^20", "debug": "^4.1.1"}, "files": ["lib"]}