# kitx
A Node.js toolkit.

[![NPM version][npm-image]][npm-url]
[![build status][travis-image]][travis-url]
[![Coverage Status](https://coveralls.io/repos/JacksonTian/kitx/badge.svg?branch=master&service=github)](https://coveralls.io/github/JacksonTian/kitx?branch=master)
[![<PERSON> de<PERSON>][david-image]][david-url]
[![npm download][download-image]][download-url]

[npm-image]: https://img.shields.io/npm/v/kitx.svg?style=flat-square
[npm-url]: https://npmjs.org/package/kitx
[travis-image]: https://img.shields.io/travis/JacksonTian/kitx.svg?style=flat-square
[travis-url]: https://travis-ci.org/JacksonTian/kitx
[david-image]: https://img.shields.io/david/<PERSON>ian/kitx.svg?style=flat-square
[david-url]: https://david-dm.org/JacksonTian/kitx
[download-image]: https://img.shields.io/npm/dm/kitx.svg?style=flat-square
[download-url]: https://npmjs.org/package/kitx

## Installation

```sh
$ npm install kitx --save
```

## Usage

Require it:

```js
var kit = require('kitx');
```

## API
See [API docs](http://doxmate.cool/JacksonTian/kitx/api.html).

## License
The MIT license.
